import { loadStripe, Stripe } from '@stripe/stripe-js'
import { supabase } from './supabase'

const stripePublishableKey = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY

if (!stripePublishableKey) {
  throw new Error('Missing VITE_STRIPE_PUBLISHABLE_KEY environment variable')
}

// Lazy load Stripe only when needed
let stripePromise: Promise<Stripe | null> | null = null

export const getStripe = () => {
  if (!stripePromise) {
    stripePromise = loadStripe(stripePublishableKey)
  }
  return stripePromise
}

// Keep backward compatibility
export const stripe = getStripe()

export interface PricingPlan {
  id: string
  name: string
  price: number // in cents
  reports: number
  priceId: string // Stripe Price ID
}

export const pricingPlans: PricingPlan[] = [
  {
    id: 'starter',
    name: 'Starter',
    price: 499, // $4.99
    reports: 3,
    priceId: 'price_1RpZ1PJ190Ki7I11Zt5E7Fzk' // Replace with actual Stripe Price ID
  },
  {
    id: 'professional', 
    name: 'Professional',
    price: 999, // $9.99
    reports: 10,
    priceId: 'price_1RpZ1fJ190Ki7I11t0JGguCe' // Replace with actual Stripe Price ID
  },
  {
    id: 'business',
    name: 'Business', 
    price: 1999, // $19.99
    reports: 25,
    priceId: 'price_1RpZ1mJ190Ki7I11KttjsEkQ' // Replace with actual Stripe Price ID
  }
]

export const createCheckoutSession = async (
  priceId: string,
  userId: string,
  planName: string,
  reports: number
) => {
  try {
    const { data, error } = await supabase.functions.invoke('create-checkout-session', {
      body: {
        priceId,
        userId,
        planName,
        reports,
        successUrl: `${window.location.origin}/payment-success`,
        cancelUrl: `${window.location.origin}/pricing`
      }
    })

    if (error) {
      console.error('Supabase function error:', error)
      if (error.message?.includes('timeout') || error.message?.includes('network')) {
        throw new Error('Network timeout. Please check your connection and try again.')
      } else if (error.message?.includes('denied') || error.message?.includes('unauthorized')) {
        throw new Error('Authentication error. Please sign in again.')
      } else {
        throw new Error(`Service error: ${error.message || 'Unknown error'}`)
      }
    }

    if (!data || !data.url) {
      throw new Error('No checkout URL returned from payment service')
    }

    return data
  } catch (error) {
    console.error('Error creating checkout session:', error)
    // Re-throw the error with more context
    if (error instanceof Error) {
      throw error
    } else {
      throw new Error('Failed to create checkout session. Please try again.')
    }
  }
} 