import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react'

// Plugin to inject preconnect hints
const injectPreconnectPlugin = () => {
  return {
    name: 'inject-preconnect',
    transformIndexHtml: {
      order: 'pre',
      handler(html: string) {
        return html.replace(
          '</head>',
          `    <!-- Preconnect to critical origins for performance -->
    <link rel="preconnect" href="https://js.stripe.com" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <!-- DNS prefetch for additional performance -->
    <link rel="dns-prefetch" href="https://api.stripe.com">
    <link rel="dns-prefetch" href="https://checkout.stripe.com">
  </head>`
        )
      }
    }
  }
};

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), injectPreconnectPlugin()],
  base: '/', // Changed from '/feedbacklens/' for Vercel
  build: {
    outDir: 'dist', // Changed from 'docs' to standard 'dist' for Vercel
    emptyOutDir: true,
    // Target modern browsers to reduce bundle size
    target: ['es2020', 'edge88', 'firefox78', 'chrome87', 'safari14'],
    // Performance optimizations
    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching
        manualChunks: {
          // Core vendor chunks
          'react-vendor': ['react', 'react-dom'],
          'router-vendor': ['react-router-dom'],
          'query-vendor': ['@tanstack/react-query'],
          'ui-vendor': ['framer-motion', 'lucide-react'],
          'supabase-vendor': ['@supabase/supabase-js'],
          'analytics-vendor': ['@vercel/analytics'],

          // Payment vendor chunk (lazy loaded)
          'payment-vendor': ['@stripe/stripe-js'],

          // Page chunks - main pages (frequently accessed)
          'pages-main': [
            './src/pages/LandingPage.tsx',
            './src/pages/PricingPage.tsx'
          ],
          // Page chunks - content pages (less frequently accessed)
          'pages-content': [
            './src/pages/AboutPage.tsx',
            './src/pages/FAQPage.tsx',
            './src/pages/HowItWorksPage.tsx'
          ],
          // Page chunks - app pages (authenticated users only)
          'pages-app': [
            './src/pages/ProfilePage.tsx',
            './src/pages/ReportPage.tsx'
          ]
        }
      }
    },
    // Optimize chunk size
    chunkSizeWarningLimit: 1000,
    // Enable source maps for production debugging
    sourcemap: false,
    // Minification options
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },
  optimizeDeps: {
    exclude: ['lucide-react'],
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@tanstack/react-query',
      '@supabase/supabase-js'
    ]
  },
  // Development server optimizations
  server: {
    hmr: {
      overlay: false
    }
  },
  // Preview server optimizations
  preview: {
    port: 4173,
    strictPort: true
  }
});
