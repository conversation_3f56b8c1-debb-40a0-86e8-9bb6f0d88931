// SEO配置文件 - 集中管理所有页面的SEO设置

export interface PageSEOConfig {
  title: string;
  description: string;
  keywords: string;
  image?: string;
  type?: 'website' | 'article' | 'product';
  structuredData?: object;
}

const baseUrl = 'https://appreview.today';
const defaultImage = '/app-review-today-192.png';

export const seoConfig: Record<string, PageSEOConfig> = {
  home: {
    title: 'AppReview.Today - AI-Powered User Review Analysis',
    description: 'Generate comprehensive reports from user reviews across App Store, Google Play, Reddit, and more. Get actionable insights in minutes with our AI-powered analysis platform.',
    keywords: 'app review analysis, user feedback analysis, app store optimization, mobile app insights, review sentiment analysis, app analytics, competitive analysis',
    type: 'website',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "AppReview.Today",
      "url": baseUrl,
      "potentialAction": {
        "@type": "SearchAction",
        "target": `${baseUrl}/search?q={search_term_string}`,
        "query-input": "required name=search_term_string"
      }
    }
  },

  pricing: {
    title: 'Pricing - AppReview.Today | Affordable App Review Analysis Plans',
    description: 'Choose the perfect plan for your app review analysis needs. Start free or upgrade to premium plans with advanced AI insights and unlimited reports.',
    keywords: 'app review analysis pricing, app analytics plans, mobile app insights cost, review analysis subscription, affordable app analytics',
    type: 'website',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "Product",
      "name": "AppReview.Today Analysis Plans",
      "description": "AI-powered app review analysis service",
      "offers": [
        {
          "@type": "Offer",
          "name": "Free Plan",
          "price": "0",
          "priceCurrency": "USD",
          "description": "1 free report to get started"
        },
        {
          "@type": "Offer", 
          "name": "Starter Plan",
          "price": "29",
          "priceCurrency": "USD",
          "description": "5 comprehensive reports"
        }
      ]
    }
  },

  demo: {
    title: 'Demo - AppReview.Today | Try Our AI Review Analysis',
    description: 'Experience our AI-powered app review analysis platform with interactive demos. See how we transform user feedback into actionable insights.',
    keywords: 'app review analysis demo, mobile app insights demo, review sentiment analysis example, app analytics preview',
    type: 'website'
  },

  profile: {
    title: 'Profile - AppReview.Today | Manage Your Account',
    description: 'Manage your AppReview.Today account, view your analysis history, and track your usage statistics.',
    keywords: 'app review analysis account, user profile, analysis history, usage statistics',
    type: 'website'
  },

  paymentSuccess: {
    title: 'Payment Successful - AppReview.Today',
    description: 'Thank you for your purchase! Your payment has been processed successfully.',
    keywords: 'payment success, purchase confirmation, app review analysis subscription',
    type: 'website'
  },

  about: {
    title: 'About Us - AppReview.Today | Our Mission & Vision',
    description: 'Learn about AppReview.Today\'s mission to democratize app review analysis through AI-powered insights. Discover our values, journey, and commitment to helping developers succeed.',
    keywords: 'about appreview.today, company mission, app review analysis team, ai-powered insights, developer tools company',
    type: 'website',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "AboutPage",
      "name": "About AppReview.Today",
      "description": "Learn about our mission to help app developers understand their users better through intelligent review analysis",
      "mainEntity": {
        "@type": "Organization",
        "name": "AppReview.Today",
        "description": "AI-powered app review analysis platform",
        "foundingDate": "2024",
        "mission": "To democratize app review analysis by providing powerful, AI-driven insights that help developers understand their users and improve their products"
      }
    }
  },

  faq: {
    title: 'FAQ - AppReview.Today | Frequently Asked Questions',
    description: 'Find answers to common questions about our app review analysis platform, pricing, features, and how to get started.',
    keywords: 'app review analysis faq, frequently asked questions, help, support, how to use',
    type: 'website',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "How does AppReview.Today analyze app reviews?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Our platform uses advanced AI technology powered by Google Gemini API to scrape reviews from multiple sources (App Store, Google Play, Reddit) and analyze them for sentiment, themes, and actionable insights."
          }
        },
        {
          "@type": "Question",
          "name": "Which platforms do you support for review analysis?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "We currently support App Store (iOS), Google Play Store (Android), and Reddit discussions for comprehensive app review analysis."
          }
        },
        {
          "@type": "Question",
          "name": "What is included in the free plan?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The free plan includes 1 comprehensive analysis report with sentiment analysis, theme extraction, and actionable recommendations."
          }
        },
        {
          "@type": "Question",
          "name": "How long does it take to generate a report?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Report generation typically takes 2-5 minutes depending on the number of reviews being analyzed, with real-time progress updates."
          }
        }
      ]
    }
  },

  howItWorks: {
    title: 'How It Works - AppReview.Today | AI Review Analysis Process',
    description: 'Learn how our AI-powered platform analyzes app reviews from multiple sources to generate actionable insights and comprehensive reports in 4 simple steps.',
    keywords: 'how app review analysis works, ai review processing, app insights generation, review analysis process, step by step guide',
    type: 'website',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "HowTo",
      "name": "How to Analyze App Reviews with AI",
      "description": "Step-by-step guide to analyzing app reviews using AI-powered insights",
      "totalTime": "PT5M",
      "supply": [
        {
          "@type": "HowToSupply",
          "name": "App name or details"
        }
      ],
      "tool": [
        {
          "@type": "HowToTool",
          "name": "AppReview.Today Platform"
        }
      ],
      "step": [
        {
          "@type": "HowToStep",
          "name": "Enter App Information",
          "text": "Provide your app name or competitor app details for automatic identification across platforms",
          "url": "https://appreview.today/how-it-works#step1"
        },
        {
          "@type": "HowToStep",
          "name": "AI Analysis",
          "text": "Our AI scrapes and analyzes reviews using advanced sentiment analysis and pattern recognition",
          "url": "https://appreview.today/how-it-works#step2"
        },
        {
          "@type": "HowToStep",
          "name": "Generate Insights",
          "text": "AI identifies themes, pain points, feature requests, and actionable recommendations",
          "url": "https://appreview.today/how-it-works#step3"
        },
        {
          "@type": "HowToStep",
          "name": "Export Report",
          "text": "Receive comprehensive PDF report with insights and recommendations for your team",
          "url": "https://appreview.today/how-it-works#step4"
        }
      ]
    }
  },

  blog: {
    title: 'Blog - AppReview.Today | App Analytics Insights & Tips',
    description: 'Stay updated with the latest trends in app review analysis, mobile app insights, and best practices for app store optimization.',
    keywords: 'app review analysis blog, mobile app insights, app store optimization tips, app analytics trends',
    type: 'website'
  },

  features: {
    title: 'Features - AppReview.Today | AI-Powered Review Analysis Tools',
    description: 'Discover powerful features of our AI-driven app review analysis platform. From sentiment analysis to real-time monitoring, see what makes us the leading choice.',
    keywords: 'app review analysis features, ai sentiment analysis, review monitoring tools, app analytics capabilities, mobile app insights features',
    type: 'website',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "AppReview.Today Features",
      "description": "Comprehensive suite of AI-powered app review analysis features",
      "mainEntity": {
        "@type": "SoftwareApplication",
        "name": "AppReview.Today",
        "applicationCategory": "BusinessApplication",
        "operatingSystem": "Web",
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD"
        },
        "featureList": [
          "AI-Powered Sentiment Analysis",
          "Multi-Platform Review Collection",
          "Real-Time Monitoring",
          "Advanced Analytics Dashboard",
          "Automated Report Generation",
          "Team Collaboration Tools"
        ]
      }
    }
  },

  useCases: {
    title: 'Use Cases - AppReview.Today | How Teams Use Our Platform',
    description: 'Explore real-world use cases for app review analysis. See how product managers, QA teams, marketers, and developers leverage our AI insights.',
    keywords: 'app review analysis use cases, product management insights, qa bug detection, marketing intelligence, customer success analytics',
    type: 'website',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "AppReview.Today Use Cases",
      "description": "Real-world applications of AI-powered app review analysis across different roles and industries",
      "mainEntity": [
        {
          "@type": "Article",
          "headline": "Product Management Use Cases",
          "description": "How product managers use review analysis for feature prioritization and roadmap planning"
        },
        {
          "@type": "Article",
          "headline": "Quality Assurance Applications",
          "description": "Automated bug detection and issue prioritization from user reviews"
        },
        {
          "@type": "Article",
          "headline": "Marketing Intelligence",
          "description": "Understanding user preferences and improving marketing messaging"
        }
      ]
    }
  },







  // 法律与合规页面
  privacyPolicy: {
    title: 'Privacy Policy - AppReview.Today | Data Protection & Privacy',
    description: 'Learn how AppReview.Today protects your privacy and handles your personal data. Comprehensive privacy policy covering data collection, usage, and your rights.',
    keywords: 'privacy policy, data protection, personal data, privacy rights, data security, GDPR compliance',
    type: 'website',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "Privacy Policy",
      "description": "Privacy policy explaining how AppReview.Today collects, uses, and protects personal data",
      "about": {
        "@type": "PrivacyPolicy",
        "name": "AppReview.Today Privacy Policy",
        "dateModified": "2025-01-15",
        "publisher": {
          "@type": "Organization",
          "name": "AppReview.Today"
        }
      }
    }
  },



  termsOfService: {
    title: 'Terms of Service - AppReview.Today | Legal Terms & Conditions',
    description: 'Read our terms of service governing the use of AppReview.Today. Clear terms covering service usage, billing, cancellation, and user responsibilities.',
    keywords: 'terms of service, terms and conditions, legal terms, service agreement, user agreement, billing terms',
    type: 'website',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "Terms of Service",
      "description": "Terms of service governing the use of AppReview.Today platform",
      "about": {
        "@type": "TermsOfService",
        "name": "AppReview.Today Terms of Service",
        "dateModified": "2025-01-15",
        "publisher": {
          "@type": "Organization",
          "name": "AppReview.Today"
        }
      }
    }
  },



  // 工具页面
  ratingCalculator: {
    title: 'App Rating Calculator - Free Tool | AppReview.Today',
    description: 'Free app rating calculator tool. Predict your app rating based on review distribution and sentiment analysis.',
    keywords: 'app rating calculator, app store rating predictor, review rating tool, free app analytics tool',
    type: 'website'
  },

  sentimentAnalyzer: {
    title: 'Review Sentiment Analyzer - Free Tool | AppReview.Today',
    description: 'Free sentiment analysis tool for app reviews. Analyze the emotional tone of user feedback instantly.',
    keywords: 'review sentiment analyzer, app review sentiment tool, free sentiment analysis, user feedback analyzer',
    type: 'website'
  },

  // 行业页面
  gamingApps: {
    title: 'Gaming Apps Analytics - AppReview.Today | Player Sentiment & Game Optimization',
    description: 'Optimize your mobile game with player sentiment analysis. Understand player feedback, improve retention, balance gameplay, and boost app store ratings for gaming apps.',
    keywords: 'gaming analytics, player sentiment, game optimization, mobile games, player feedback, game development, retention analysis',
    type: 'website',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "Gaming Apps Analytics - AppReview.Today",
      "description": "Player sentiment analysis and game optimization platform for mobile gaming apps",
      "audience": {
        "@type": "Audience",
        "audienceType": "Game Developers"
      },
      "about": {
        "@type": "Thing",
        "name": "Mobile Gaming Analytics"
      }
    }
  },

  ecommerceApps: {
    title: 'E-commerce Apps Analytics - AppReview.Today | Customer Experience Optimization',
    description: 'Optimize your e-commerce app with customer feedback analysis. Improve shopping experience, reduce cart abandonment, boost conversions and customer satisfaction.',
    keywords: 'ecommerce analytics, customer feedback, shopping experience, conversion optimization, retail apps, customer satisfaction',
    type: 'website',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "E-commerce Apps Analytics - AppReview.Today",
      "description": "Customer experience optimization platform for e-commerce and retail mobile apps",
      "audience": {
        "@type": "Audience",
        "audienceType": "E-commerce Developers"
      },
      "about": {
        "@type": "Thing",
        "name": "E-commerce Analytics"
      }
    }
  },

  fintechApps: {
    title: 'Fintech Apps Analytics - AppReview.Today | Financial App User Experience',
    description: 'Optimize your fintech app with user feedback analysis. Improve security trust, streamline payments, enhance user experience and boost financial app ratings.',
    keywords: 'fintech analytics, financial apps, user trust, payment optimization, banking apps, financial UX, security feedback',
    type: 'website',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "Fintech Apps Analytics - AppReview.Today",
      "description": "User experience optimization platform for fintech and financial services mobile apps",
      "audience": {
        "@type": "Audience",
        "audienceType": "Fintech Developers"
      },
      "about": {
        "@type": "Thing",
        "name": "Fintech Analytics"
      }
    }
  },

  // 比较页面
  vsAppFollow: {
    title: 'AppReview.Today vs AppFollow | Feature Comparison',
    description: 'Compare AppReview.Today with AppFollow. See why our AI-powered analysis provides deeper insights at better value.',
    keywords: 'appreview.today vs appfollow, app analytics comparison, review analysis tools comparison',
    type: 'website'
  },

  vsSensorTower: {
    title: 'AppReview.Today vs Sensor Tower | Detailed Comparison',
    description: 'Compare AppReview.Today with Sensor Tower. Discover our unique AI-powered approach to app review analysis.',
    keywords: 'appreview.today vs sensor tower, app intelligence comparison, mobile app analytics tools',
    type: 'website'
  },

  // 博客页面SEO配置 - Updated the first blog entry instead
  blogUpdated: {
    title: 'Blog - AppReview.Today | App Analytics Insights & Tutorials',
    description: 'Discover expert insights on app analytics, user feedback analysis, and mobile app optimization. Learn from industry experts and improve your app\'s performance with our comprehensive guides and tutorials.',
    keywords: 'app analytics blog, mobile app insights, user feedback analysis tutorials, app store optimization guides, sentiment analysis tips, app development best practices',
    type: 'website',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "Blog",
      "name": "AppReview.Today Blog",
      "description": "Expert insights and tutorials on app analytics and user feedback analysis",
      "url": `${baseUrl}/blog`,
      "publisher": {
        "@type": "Organization",
        "name": "AppReview.Today",
        "logo": {
          "@type": "ImageObject",
          "url": `${baseUrl}/logo.png`
        }
      }
    }
  },

  // 用户角色页面SEO配置

  forProductManagers: {
    title: 'For Product Managers - AppReview.Today | Strategic Product Insights',
    description: 'Transform user feedback into product strategy. Competitive analysis, feature validation, user journey mapping, and data-driven roadmap prioritization for product managers.',
    keywords: 'product management, user feedback analysis, product strategy, feature validation, competitive analysis, product roadmap',
    type: 'website',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "AppReview.Today for Product Managers",
      "description": "Strategic product insights platform designed for product managers and product teams",
      "audience": {
        "@type": "Audience",
        "audienceType": "Product Managers"
      }
    }
  },

  forMarketers: {
    title: 'For Marketers - AppReview.Today | Brand Sentiment & Campaign Analytics',
    description: 'Transform user feedback into marketing gold. Brand sentiment analysis, campaign performance tracking, audience insights, and content optimization for marketing teams.',
    keywords: 'marketing analytics, brand sentiment, campaign tracking, audience insights, content optimization, social media marketing',
    type: 'website',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "AppReview.Today for Marketers",
      "description": "Marketing analytics platform designed for digital marketers and growth teams",
      "audience": {
        "@type": "Audience",
        "audienceType": "Marketers"
      }
    }
  },

  forResearchers: {
    title: 'For Researchers - AppReview.Today | Academic & Market Research Platform',
    description: 'Advanced analytics platform for researchers. Statistical modeling, data export, research reports, and publication-ready insights for academic and market research.',
    keywords: 'research platform, academic research, market research, statistical analysis, data export, research methodology',
    type: 'website',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "WebPage",
      "name": "AppReview.Today for Researchers",
      "description": "Research analytics platform designed for academic researchers and market analysts",
      "audience": {
        "@type": "Audience",
        "audienceType": "Researchers"
      }
    }
  }
};

// 获取页面SEO配置的辅助函数
export function getPageSEO(pageKey: string, customData?: Partial<PageSEOConfig>): PageSEOConfig {
  const config = seoConfig[pageKey] || seoConfig.home;
  
  return {
    ...config,
    image: config.image || defaultImage,
    ...customData
  };
}

// 生成动态页面SEO配置
export function generateReportPageSEO(reportId: string, appName?: string): PageSEOConfig {
  const title = appName 
    ? `${appName} Review Analysis Report | AppReview.Today`
    : `App Review Analysis Report | AppReview.Today`;
    
  const description = appName
    ? `Comprehensive AI-powered analysis of ${appName} user reviews. Get insights on user sentiment, feature requests, and improvement opportunities.`
    : 'Comprehensive AI-powered app review analysis report with user sentiment insights and actionable recommendations.';

  return {
    title,
    description,
    keywords: 'app review analysis report, user feedback insights, app sentiment analysis, mobile app analytics report',
    type: 'article',
    structuredData: {
      "@context": "https://schema.org",
      "@type": "Report",
      "name": title,
      "description": description,
      "url": `${baseUrl}/report/${reportId}`,
      "datePublished": new Date().toISOString(),
      "author": {
        "@type": "Organization",
        "name": "AppReview.Today"
      }
    }
  };
}
