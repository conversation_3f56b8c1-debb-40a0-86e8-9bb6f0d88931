import React from 'react';

// Performance monitoring utilities
export const performanceMonitor = {
  // Measure Core Web Vitals
  measureCLS: () => {
    if ('web-vital' in window) {
      // @ts-ignore
      import('web-vitals').then(({ getCLS }) => {
        getCLS(console.log);
      });
    }
  },

  measureFID: () => {
    if ('web-vital' in window) {
      // @ts-ignore
      import('web-vitals').then(({ getFID }) => {
        getFID(console.log);
      });
    }
  },

  measureLCP: () => {
    if ('web-vital' in window) {
      // @ts-ignore
      import('web-vitals').then(({ getLCP }) => {
        getLCP(console.log);
      });
    }
  },

  // Measure resource loading times
  measureResourceTiming: () => {
    if ('performance' in window && 'getEntriesByType' in performance) {
      const resources = performance.getEntriesByType('resource');
      const slowResources = resources.filter((resource: any) => resource.duration > 1000);
      
      if (slowResources.length > 0) {
        console.warn('Slow loading resources:', slowResources);
      }
    }
  },

  // Monitor Stripe loading specifically
  monitorStripeLoading: () => {
    const startTime = performance.now();
    
    return {
      end: () => {
        const endTime = performance.now();
        const duration = endTime - startTime;
        
        if (duration > 2000) {
          console.warn(`Stripe loading took ${duration.toFixed(2)}ms`);
        } else {
          console.log(`Stripe loaded in ${duration.toFixed(2)}ms`);
        }
      }
    };
  },

  // Initialize all monitoring
  init: () => {
    if (process.env.NODE_ENV === 'production') {
      // Only monitor in production
      performanceMonitor.measureCLS();
      performanceMonitor.measureFID();
      performanceMonitor.measureLCP();
      
      // Monitor resources after page load
      window.addEventListener('load', () => {
        setTimeout(() => {
          performanceMonitor.measureResourceTiming();
        }, 1000);
      });
    }
  }
};

// Lazy loading utility for heavy components
export const createLazyComponent = <T extends React.ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  fallback?: React.ComponentType
) => {
  const LazyComponent = React.lazy(importFn);
  
  return (props: React.ComponentProps<T>) => (
    <React.Suspense fallback={fallback ? React.createElement(fallback) : <div>Loading...</div>}>
      <LazyComponent {...props} />
    </React.Suspense>
  );
};
